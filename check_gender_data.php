<?php
require_once 'admin/includes/config.php';

echo "=== CHECKING PASSENGER GENDER DISTRIBUTION ===\n\n";

// Check if passenger_manifest table exists
$table_check = $con->query("SHOW TABLES LIKE 'passenger_manifest'");
if ($table_check->num_rows > 0) {
    echo "✓ passenger_manifest table exists\n";
    
    // Check table structure
    echo "\n--- Table Structure ---\n";
    $structure = $con->query("DESCRIBE passenger_manifest");
    while ($row = $structure->fetch_assoc()) {
        if ($row['Field'] == 'sex') {
            echo "Sex column: {$row['Field']} - Type: {$row['Type']}\n";
        }
    }
    
    // Get total count
    $total_result = $con->query("SELECT COUNT(*) as total FROM passenger_manifest");
    $total = $total_result->fetch_assoc()['total'];
    echo "\nTotal passengers in manifest: $total\n";
    
    if ($total > 0) {
        // Get gender distribution
        echo "\n--- Gender Distribution ---\n";
        $gender_result = $con->query("SELECT sex, COUNT(*) as count FROM passenger_manifest GROUP BY sex");
        while ($row = $gender_result->fetch_assoc()) {
            $percentage = round(($row['count'] / $total) * 100, 1);
            echo "{$row['sex']}: {$row['count']} ({$percentage}%)\n";
        }
        
        // Check for specific Male/Female counts
        echo "\n--- Chart Data (Male/Female only) ---\n";
        $chart_result = $con->query("SELECT sex, COUNT(*) as count FROM passenger_manifest WHERE sex IN ('Male', 'Female') GROUP BY sex");
        $male = 0; $female = 0;
        while ($row = $chart_result->fetch_assoc()) {
            if ($row['sex'] === 'Male') $male = $row['count'];
            if ($row['sex'] === 'Female') $female = $row['count'];
        }
        echo "Male: $male\n";
        echo "Female: $female\n";
        echo "Total for chart: " . ($male + $female) . "\n";
    }
} else {
    echo "✗ passenger_manifest table does not exist\n";
    echo "Checking bookings table instead...\n\n";
    
    // Check bookings table
    $bookings_check = $con->query("SHOW TABLES LIKE 'bookings'");
    if ($bookings_check->num_rows > 0) {
        echo "✓ bookings table exists\n";
        
        // Check table structure
        echo "\n--- Bookings Table Structure ---\n";
        $structure = $con->query("DESCRIBE bookings");
        while ($row = $structure->fetch_assoc()) {
            if ($row['Field'] == 'sex') {
                echo "Sex column: {$row['Field']} - Type: {$row['Type']}\n";
            }
        }
        
        // Get total count
        $total_result = $con->query("SELECT COUNT(*) as total FROM bookings WHERE sex IS NOT NULL");
        $total = $total_result->fetch_assoc()['total'];
        echo "\nTotal bookings with gender data: $total\n";
        
        if ($total > 0) {
            // Get gender distribution
            echo "\n--- Gender Distribution (Bookings) ---\n";
            $gender_result = $con->query("SELECT sex, COUNT(*) as count FROM bookings WHERE sex IS NOT NULL GROUP BY sex");
            while ($row = $gender_result->fetch_assoc()) {
                $percentage = round(($row['count'] / $total) * 100, 1);
                echo "{$row['sex']}: {$row['count']} ({$percentage}%)\n";
            }
        }
    }
}

echo "\n=== TESTING get-gender-stats.php OUTPUT ===\n";
// Simulate the get-gender-stats.php logic
try {
    $response = [
        'success' => true,
        'male' => 0,
        'female' => 0,
        'total' => 0
    ];
    
    // Check if passenger_manifest table exists
    $table_check = $con->query("SHOW TABLES LIKE 'passenger_manifest'");
    
    if ($table_check->num_rows > 0) {
        // Get gender distribution from passenger_manifest
        $stmt = $con->prepare("SELECT sex, COUNT(*) as count FROM passenger_manifest WHERE sex IN ('Male', 'Female') GROUP BY sex");
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            if ($row['sex'] === 'Male') {
                $response['male'] = intval($row['count']);
            } elseif ($row['sex'] === 'Female') {
                $response['female'] = intval($row['count']);
            }
        }
        
        $response['total'] = $response['male'] + $response['female'];
        
    } else {
        // Fallback: Get gender from bookings table
        $stmt = $con->prepare("SELECT sex, COUNT(*) as count FROM bookings WHERE sex IN ('Male', 'Female', 'male', 'female') GROUP BY sex");
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $sex = strtolower($row['sex']);
            if ($sex === 'male') {
                $response['male'] = intval($row['count']);
            } elseif ($sex === 'female') {
                $response['female'] = intval($row['count']);
            }
        }

        $response['total'] = $response['male'] + $response['female'];
    }
    
    // If no data found, provide sample data
    if ($response['total'] === 0) {
        $response['male'] = 45;
        $response['female'] = 55;
        $response['total'] = 100;
        $response['note'] = 'Sample data - no real data available';
    }
    
    echo "API Response would be: " . json_encode($response, JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
