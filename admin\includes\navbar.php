<!-- Navbar -->
<nav class="main-header navbar navbar-expand navbar-white navbar-light">
  <!-- Left navbar links -->
  <ul class="navbar-nav">
    <li class="nav-item">
      <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
    </li>
  </ul>

  <!-- Right navbar links -->
  <ul class="navbar-nav ml-auto">
    <!-- Notifications Dropdown Menu -->
    <li class="nav-item dropdown">
      <a class="nav-link" data-toggle="dropdown" href="#" id="notifBell" title="Notifications">
        <i class="far fa-bell"></i>
        <span class="badge badge-danger navbar-badge" id="notifCount" style="display:none;">0</span>
      </a>
      <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right" id="notifDropdown">
        <span class="dropdown-header">
          <i class="fas fa-bell mr-2"></i>Notifications
        </span>
        <div class="dropdown-divider"></div>
        <div id="notifList" style="max-height: 300px; overflow-y: auto;">
          <span class="dropdown-item text-muted">
            <i class="fas fa-info-circle mr-2"></i>No new notifications
          </span>
        </div>
        <div class="dropdown-divider"></div>
        <a href="#" class="dropdown-item dropdown-footer text-center">
          <i class="fas fa-eye mr-2"></i>See All Notifications
        </a>
      </div>
    </li>
    <!-- Fullscreen Button -->
    <li class="nav-item">
      <a class="nav-link" data-widget="fullscreen" href="#" role="button" title="Toggle Fullscreen">
        <i class="fas fa-expand-arrows-alt"></i>
      </a>
    </li>
    <!-- User Menu -->
    <li class="nav-item dropdown">
      <a class="nav-link" data-toggle="dropdown" href="#" title="User Menu">
        <i class="fas fa-user-circle"></i>
      </a>
      <div class="dropdown-menu dropdown-menu-right">
        <a href="<?= ADMIN_BASE_URL ?>pages/profile.php" class="dropdown-item">
          <i class="fas fa-user mr-2"></i>Profile
        </a>
        <a href="<?= ADMIN_BASE_URL ?>pages/change-password.php" class="dropdown-item">
          <i class="fas fa-key mr-2"></i>Change Password
        </a>
        <div class="dropdown-divider"></div>
        <a href="<?= ADMIN_BASE_URL ?>pages/logout.php" class="dropdown-item">
          <i class="fas fa-sign-out-alt mr-2"></i>Logout
        </a>
      </div>
    </li>
  </ul>
</nav>
<!-- /.navbar -->

<!-- Notification Bell Script -->
<script>
$(document).ready(function() {
  // Initialize notification system
  loadNotifications();

  // Refresh notifications every 30 seconds
  setInterval(loadNotifications, 30000);
});

function loadNotifications() {
  // You can replace this with actual AJAX call to fetch notifications
  // For now, using sample data - change to 3 to test notifications
  var notifCount = 3; // Change this to test notifications

  if (notifCount > 0) {
    $('#notifCount').text(notifCount).show();
    $('#notifList').html(
      '<a href="#" class="dropdown-item">' +
      '<i class="fas fa-calendar-check text-success mr-2"></i>' +
      '<div><strong>New Booking</strong><br><small class="text-muted">5 minutes ago</small></div>' +
      '</a>' +
      '<div class="dropdown-divider"></div>' +
      '<a href="#" class="dropdown-item">' +
      '<i class="fas fa-user-plus text-info mr-2"></i>' +
      '<div><strong>New User Registration</strong><br><small class="text-muted">10 minutes ago</small></div>' +
      '</a>' +
      '<div class="dropdown-divider"></div>' +
      '<a href="#" class="dropdown-item">' +
      '<i class="fas fa-exclamation-triangle text-warning mr-2"></i>' +
      '<div><strong>System Alert</strong><br><small class="text-muted">1 hour ago</small></div>' +
      '</a>'
    );
  } else {
    $('#notifCount').hide();
    $('#notifList').html(
      '<span class="dropdown-item text-muted">' +
      '<i class="fas fa-check-circle text-success mr-2"></i>' +
      'No new notifications' +
      '</span>'
    );
  }
}

// Add some styling for better visibility
$('<style>')
  .prop('type', 'text/css')
  .html(`
    .navbar-nav .nav-link {
      color: #495057 !important;
      font-size: 1.1rem;
    }
    .navbar-nav .nav-link:hover {
      color: #007bff !important;
    }
    .navbar-badge {
      font-size: 0.75rem;
      font-weight: bold;
    }
    .dropdown-menu {
      border: 1px solid #dee2e6;
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
  `)
  .appendTo('head');
</script>
