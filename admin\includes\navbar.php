<!-- Navbar -->
<nav class="main-header navbar navbar-expand navbar-white navbar-light" style="position:fixed;top:0;left:0;width:100%;z-index:1050;">
  <!-- Left navbar links -->
  <ul class="navbar-nav">
    <li class="nav-item">
      <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
    </li>
  </ul>

  <!-- Right navbar links -->
  <ul class="navbar-nav ml-auto">
    <!-- Notifications Dropdown Menu -->
    <li class="nav-item dropdown">
      <a class="nav-link" data-toggle="dropdown" href="#" id="notifBell">
        <i class="far fa-bell"></i>
        <span class="badge badge-danger navbar-badge" id="notifCount" style="display:none;">0</span>
      </a>
      <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right" id="notifDropdown">
        <span class="dropdown-header">Notifications</span>
        <div class="dropdown-divider"></div>
        <div id="notifList" style="max-height: 300px; overflow-y: auto;">
          <span class="dropdown-item">No new notifications</span>
        </div>
        <div class="dropdown-divider"></div>
        <a href="#" class="dropdown-item dropdown-footer">See All Notifications</a>
      </div>
    </li>
    <!-- Fullscreen Button -->
    <li class="nav-item">
      <a class="nav-link" data-widget="fullscreen" href="#" role="button">
        <i class="fas fa-expand-arrows-alt"></i>
      </a>
    </li>
  </ul>
</nav>
<!-- /.navbar -->

<!-- Notification Bell Script (Sample, replace with your AJAX logic) -->
<script>
// Example: Simulate notification count
$(document).ready(function() {
  // Simulate fetching notification count
  var notifCount = 3; // Replace with AJAX call
  if (notifCount > 0) {
    $('#notifCount').text(notifCount).show();
    $('#notifList').html('<a href="#" class="dropdown-item">You have ' + notifCount + ' new notifications</a>');
  } else {
    $('#notifCount').hide();
    $('#notifList').html('<span class="dropdown-item">No new notifications</span>');
  }
});
</script>
