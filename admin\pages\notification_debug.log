2025-07-29 11:12:42 - Starting notification fetch
2025-07-29 17:12:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:12:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:12:42 - Found 0 notifications
2025-07-29 11:12:42 - Starting notification fetch
2025-07-29 17:12:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:12:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDE<PERSON> BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:12:42 - Found 0 notifications
2025-07-29 11:13:42 - Starting notification fetch
2025-07-29 17:13:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:13:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:13:42 - Found 0 notifications
2025-07-29 11:13:42 - Starting notification fetch
2025-07-29 17:13:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:13:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:13:42 - Found 0 notifications
2025-07-29 11:14:42 - Starting notification fetch
2025-07-29 17:14:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:14:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:14:42 - Found 0 notifications
2025-07-29 11:14:42 - Starting notification fetch
2025-07-29 17:14:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:14:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:14:42 - Found 0 notifications
2025-07-29 11:15:42 - Starting notification fetch
2025-07-29 17:15:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:15:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:15:42 - Found 0 notifications
2025-07-29 11:15:42 - Starting notification fetch
2025-07-29 17:15:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:15:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:15:42 - Found 0 notifications
2025-07-29 11:16:42 - Starting notification fetch
2025-07-29 17:16:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:16:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:16:42 - Found 0 notifications
2025-07-29 11:16:42 - Starting notification fetch
2025-07-29 17:16:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:16:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:16:42 - Found 0 notifications
2025-07-29 11:17:42 - Starting notification fetch
2025-07-29 17:17:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:17:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:17:42 - Found 0 notifications
2025-07-29 11:17:42 - Starting notification fetch
2025-07-29 17:17:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:17:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:17:42 - Found 0 notifications
2025-07-29 11:18:42 - Starting notification fetch
2025-07-29 17:18:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:18:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:18:42 - Found 0 notifications
2025-07-29 11:18:42 - Starting notification fetch
2025-07-29 17:18:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:18:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:18:42 - Found 0 notifications
2025-07-29 11:19:42 - Starting notification fetch
2025-07-29 17:19:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:19:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:19:42 - Found 0 notifications
2025-07-29 11:19:42 - Starting notification fetch
2025-07-29 17:19:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:19:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:19:42 - Found 0 notifications
2025-07-29 11:20:42 - Starting notification fetch
2025-07-29 17:20:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:20:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:20:42 - Found 0 notifications
2025-07-29 11:20:42 - Starting notification fetch
2025-07-29 17:20:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:20:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:20:42 - Found 0 notifications
2025-07-29 11:21:42 - Starting notification fetch
2025-07-29 17:21:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:21:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:21:42 - Found 0 notifications
2025-07-29 11:21:42 - Starting notification fetch
2025-07-29 17:21:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:21:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:21:42 - Found 0 notifications
2025-07-29 11:22:42 - Starting notification fetch
2025-07-29 17:22:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:22:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:22:42 - Found 0 notifications
2025-07-29 11:22:42 - Starting notification fetch
2025-07-29 17:22:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:22:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:22:42 - Found 0 notifications
2025-07-29 11:23:42 - Starting notification fetch
2025-07-29 17:23:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:23:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:23:42 - Found 0 notifications
2025-07-29 11:23:42 - Starting notification fetch
2025-07-29 17:23:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:23:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:23:42 - Found 0 notifications
2025-07-29 11:24:42 - Starting notification fetch
2025-07-29 17:24:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:24:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:24:42 - Found 0 notifications
2025-07-29 11:24:42 - Starting notification fetch
2025-07-29 17:24:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:24:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:24:42 - Found 0 notifications
2025-07-29 11:25:42 - Starting notification fetch
2025-07-29 17:25:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:25:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:25:42 - Found 0 notifications
2025-07-29 11:25:42 - Starting notification fetch
2025-07-29 17:25:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:25:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:25:42 - Found 0 notifications
2025-07-29 11:26:42 - Starting notification fetch
2025-07-29 17:26:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:26:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:26:42 - Found 0 notifications
2025-07-29 11:26:42 - Starting notification fetch
2025-07-29 17:26:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:26:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:26:42 - Found 0 notifications
2025-07-29 11:27:42 - Starting notification fetch
2025-07-29 17:27:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:27:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:27:42 - Found 0 notifications
2025-07-29 11:27:42 - Starting notification fetch
2025-07-29 17:27:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:27:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:27:42 - Found 0 notifications
2025-07-29 11:28:42 - Starting notification fetch
2025-07-29 17:28:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:28:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:28:42 - Found 0 notifications
2025-07-29 11:28:42 - Starting notification fetch
2025-07-29 17:28:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:28:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:28:42 - Found 0 notifications
2025-07-29 11:29:42 - Starting notification fetch
2025-07-29 17:29:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:29:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:29:42 - Found 0 notifications
2025-07-29 11:29:42 - Starting notification fetch
2025-07-29 17:29:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:29:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:29:42 - Found 0 notifications
2025-07-29 11:30:42 - Starting notification fetch
2025-07-29 17:30:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:30:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:30:42 - Found 0 notifications
2025-07-29 11:30:42 - Starting notification fetch
2025-07-29 17:30:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:30:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:30:42 - Found 0 notifications
2025-07-29 11:31:42 - Starting notification fetch
2025-07-29 17:31:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:31:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:31:42 - Found 0 notifications
2025-07-29 11:31:42 - Starting notification fetch
2025-07-29 17:31:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-29 17:31:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-29 17:31:42 - Found 0 notifications
2025-07-29 11:32:42 - Starting notification fetch
