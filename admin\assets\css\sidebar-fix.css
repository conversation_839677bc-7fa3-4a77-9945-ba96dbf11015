/* Hide scrollbars only for sidebar elements */
.main-sidebar,
.main-sidebar *,
.sidebar,
.sidebar * {
    -ms-overflow-style: none !important;  /* IE and Edge */
    scrollbar-width: none !important;  /* Firefox */
}

/* For WebKit browsers (Chrome, Safari) - sidebar only */
.main-sidebar::-webkit-scrollbar,
.main-sidebar *::-webkit-scrollbar,
.sidebar::-webkit-scrollbar,
.sidebar *::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* Sidebar fixes - completely remove scrollbar */
.main-sidebar {
    height: 100vh !important;
    min-height: 100% !important;
    position: fixed !important;
    overflow: hidden !important;
    padding-bottom: 0 !important;
}

/* Make nav menu fit without scrollbar */
.nav-sidebar {
    overflow: hidden !important;
}

/* Adjust sidebar height to fit content */
.sidebar-mini .main-sidebar,
.sidebar-mini-md .main-sidebar,
.sidebar-mini-xs .main-sidebar {
    overflow: hidden !important;
}

.sidebar {
    padding-bottom: 0 !important;
    overflow: hidden !important;
}

/* Adjust nav items to fit without scrollbar - compact design */
.nav-sidebar .nav-item {
    margin-bottom: 0.1rem !important;
}

/* Adjust padding for compact design */
.nav-sidebar .nav-link {
    padding: 0.5rem 0.8rem !important;
    font-size: 0.95rem !important;
}

/* Compact treeview items */
.nav-treeview .nav-link {
    padding: 0.4rem 0.8rem 0.4rem 2rem !important;
    font-size: 0.9rem !important;
}

/* Compact headers */
.nav-header {
    padding: 0.5rem 0.8rem 0.3rem 0.8rem !important;
    font-size: 1rem !important;
    margin-bottom: 0.3rem !important;
}

/* Header and layout fixes for AdminLTE sidebar */
body.sidebar-mini.layout-fixed .main-header {
    position: fixed;
    top: 0;
    left: 250px;
    width: calc(100% - 250px);
    z-index: 1030;
    margin-left: 0;
    height: 57px;
    min-height: 57px;
}

/* Responsive adjustments for header */
@media (max-width: 991.98px) {
    body.sidebar-mini.layout-fixed .main-header {
        left: 0;
        width: 100%;
    }
}

/* Fix content wrapper to allow scrolling for main content */
.content-wrapper {
    min-height: calc(100vh - 70px) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

/* Fix footer position */
.main-footer {
    margin-left: 250px;
}

@media (max-width: 991.98px) {
    .main-footer {
        margin-left: 0;
    }
}

/* Fix for sidebar collapse */
body.sidebar-collapse .main-footer {
    margin-left: 4.6rem;
}

@media (max-width: 991.98px) {
    body.sidebar-collapse .main-footer {
        margin-left: 0;
    }
}
