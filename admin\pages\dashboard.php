<?php
/**
 * Admin Dashboard

 */

// Production error handling
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

// Define admin system constants
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}
if (!defined('ADMIN_ROOT')) {
    define('ADMIN_ROOT', __DIR__ . '/../');
}
if (!defined('ADMIN_BASE_URL')) {
    define('ADMIN_BASE_URL', '/Online Booking Reservation System/admin/');
}

// Include admin configuration
require_once ADMIN_ROOT . 'includes/config.php';
checkLogin();

// For now, use simple permission checking
require_once ADMIN_ROOT . 'classes/AdminAuth.php';
$auth = new AdminAuth($con, null);
$auth->requirePermission('view_dashboard');

// Get admin details
$admin_id = isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : $_SESSION['aid'];
$admin = getAdminDetails($admin_id);

// Calculate basic stats
$todaysBookings = 0;
$totalReservations = 0;
$rejectedbookings = 0;
$newbookings = 0;
$acceptedbookings = 0;
$totalPassengers = 0;

try {
    // Today's bookings
    $today = date('Y-m-d');
    $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE DATE(created_at) = ?");
    $stmt->bind_param("s", $today);
    $stmt->execute();
    $todaysBookings = $stmt->get_result()->fetch_row()[0];

    // Total reservations
    $stmt = $con->prepare("SELECT COUNT(*) FROM bookings");
    $stmt->execute();
    $totalReservations = $stmt->get_result()->fetch_row()[0];

    // Rejected bookings (cancelled or rejected)
    $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE booking_status IN ('cancelled', 'rejected')");
    $stmt->execute();
    $rejectedbookings = $stmt->get_result()->fetch_row()[0];

    // New bookings (pending)
    $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE booking_status='pending'");
    $stmt->execute();
    $newbookings = $stmt->get_result()->fetch_row()[0];

    // Accepted bookings (confirmed or accepted)
    $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE booking_status IN ('confirmed', 'accepted')");
    $stmt->execute();
    $acceptedbookings = $stmt->get_result()->fetch_row()[0];

    // Total passengers
    $stmt = $con->prepare("SELECT COUNT(*) FROM passenger_manifest");
    $stmt->execute();
    $totalPassengers = $stmt->get_result()->fetch_row()[0];

} catch (Exception $e) {
    error_log("Dashboard stats error: " . $e->getMessage());
}

// Calculate available boats dynamically
$availableBoats = 0;
try {
    $stmt = $con->prepare("SELECT COUNT(*) FROM boats WHERE availability_status = 'available'");
    $stmt->execute();
    $availableBoats = $stmt->get_result()->fetch_row()[0];
} catch (Exception $e) {
    $availableBoats = 0;
}

// Get login success message
$login_success = '';
if (isset($_SESSION['login_success'])) {
    $login_success = $_SESSION['login_success'];
    unset($_SESSION['login_success']);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Admin Dashboard | Balangay Boat Tours</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- AdminLTE -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <!-- Sidebar Fix CSS -->
  <link rel="stylesheet" href="../assets/css/sidebar-fix.css">
  
  <style>
    .dashboard-section-title {
      color: #333;
      font-weight: 600;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #007bff;
    }
    .card:hover {
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
      transition: box-shadow 0.3s ease;
    }

    /* Fix layout for no-scroll sidebar */
    body.sidebar-mini.layout-fixed .main-sidebar {
      position: fixed;
      height: 100vh;
      overflow: hidden;
      z-index: 1020;
    }

    /* Ensure sidebar content fits without scrolling */
    .main-sidebar .sidebar {
      overflow: hidden;
      height: calc(100vh - 60px);
      display: flex;
      flex-direction: column;
    }

    /* Make nav sidebar fit content */
    .main-sidebar .nav-sidebar {
      overflow: hidden;
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    /* Ensure main content can scroll */
    .content-wrapper {
      min-height: 100vh;
      background: #f4f6f9;
      overflow-y: auto;
      overflow-x: hidden;
    }
    /* Remove forced height/overflow from html, body, .wrapper */

    /* Fix navbar positioning */
    body.sidebar-mini.layout-fixed .main-header {
      position: fixed;
      top: 0;
      left: 250px;
      width: calc(100% - 250px);
      z-index: 1030;
      margin-left: 0;
      height: 57px;
      min-height: 57px;
    }

    /* Content wrapper adjustments */
    body.sidebar-mini.layout-fixed .content-wrapper {
      margin-left: 250px;
      margin-top: 57px;
      min-height: calc(100vh - 57px);
      padding-top: 0;
    }

    /* Ensure proper scrolling for content */
    .content-wrapper {
      overflow-x: hidden;
    }

    /* Fix footer positioning */
    body.sidebar-mini.layout-fixed .main-footer {
      margin-left: 250px;
    }

    /* Responsive adjustments */
    @media (max-width: 991.98px) {
      body.sidebar-mini.layout-fixed .main-header {
        left: 0;
        width: 100%;
      }
      body.sidebar-mini.layout-fixed .content-wrapper {
        margin-left: 0;
      }
      body.sidebar-mini.layout-fixed .main-footer {
        margin-left: 0;
      }
    }

    /* Additional fixes for smooth scrolling */
    html {
      scroll-behavior: smooth;
    }

    /* Ensure navbar doesn't interfere with content */
    .navbar {
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Fix navbar styling */
    .main-header.navbar {
      z-index: 1030 !important;
      padding: 0.75rem 1rem;
      margin-bottom: 0;
      height: 57px;
      min-height: 57px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Fix navbar items alignment */
    .navbar-nav {
      display: flex;
      align-items: center;
      height: 100%;
    }

    .navbar-nav .nav-item {
      display: flex;
      align-items: center;
    }

    .navbar-nav .nav-link {
      display: flex;
      align-items: center;
      padding: 0.5rem 0.75rem;
      height: auto;
    }

    .main-sidebar {
      z-index: 1020 !important;
    }

    /* Additional sidebar scroll fixes */
    .sidebar-dark-primary .nav-sidebar > .nav-item {
      margin-bottom: 0;
    }

    /* Ensure only main sidebar scrolls */
    .main-sidebar .os-content {
      overflow: visible !important;
    }

    .main-sidebar .os-scrollbar {
      display: none !important;
    }

    /* Custom scrollbar for sidebar */
    .main-sidebar::-webkit-scrollbar {
      width: 6px;
    }

    .main-sidebar::-webkit-scrollbar-track {
      background: #2c3e50;
    }

    .main-sidebar::-webkit-scrollbar-thumb {
      background: #34495e;
      border-radius: 3px;
    }

    .main-sidebar::-webkit-scrollbar-thumb:hover {
      background: #4a5f7a;
    }

    /* Ensure content has proper padding */
    .content-header {
      padding: 1rem 1rem 0.5rem;
      margin-bottom: 0;
    }

    .content {
      padding: 0 1rem 1rem;
    }

    /* Fix content header title spacing */
    .content-header h1 {
      margin: 0;
      font-size: 1.8rem;
    }

    /* Ensure proper spacing for dashboard sections */
    .dashboard-section-title {
      margin-top: 1.5rem;
      margin-bottom: 1rem;
    }
  </style>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <!-- Navbar -->
  <?php include('../includes/navbar.php'); ?>

  <!-- Main Sidebar Container -->
  <?php include('../includes/sidebar.php'); ?>

  <!-- Content Wrapper -->
  <div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
      <div class="container-fluid">
        
        <?php if (!empty($login_success)): ?>
        <!-- Login Success Message -->
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <i class="fas fa-check-circle mr-2"></i> <?php echo htmlspecialchars($login_success); ?>
          <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <?php endif; ?>

      </div>
    </div>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">

        <!-- Welcome Message -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card bg-gradient-primary text-white">
              <div class="card-body">
                <div class="row align-items-center">
                  <div class="col-md-8">
                    <h4 class="mb-2">
                      <i class="fas fa-tachometer-alt mr-2"></i>
                      Welcome <?php echo htmlspecialchars($admin['name']); ?>
                    </h4>
                    <p class="mb-0">
                      Monitor your booking system, manage reservations, and track passenger manifests all in one place.<br>
                      <small>Last updated: <?php echo date('F j, Y g:i A'); ?></small>
                    </p>
                  </div>
                  <div class="col-md-4 text-right">
                    <i class="fas fa-ship" style="font-size: 4rem; opacity: 0.3;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- STAT CARDS -->
        <div class="row mb-4">
          <?php if ($auth->hasPermission('view_bookings')): ?>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card text-white" style="background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); border: none; border-radius: 10px; height: 120px; cursor: pointer;" onclick="window.location.href='all-booking.php'">
              <div class="card-body d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-0" style="font-weight: 700; font-size: 2.5rem;"><?php echo $totalReservations; ?></h2>
                  <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">TOTAL BOOKINGS</p>
                </div>
                <div>
                  <i class="fas fa-users" style="font-size: 2.5rem; opacity: 0.3;"></i>
                </div>
              </div>
            </div>
          </div>
          <?php endif; ?>

          <?php if ($auth->hasPermission('manage_admins')): ?>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card text-white" style="background: linear-gradient(135deg, #212121 0%, #616161 100%); border: none; border-radius: 10px; height: 120px; cursor: pointer;" onclick="window.location.href='manage-admin-permissions.php'">
              <div class="card-body d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-0" style="font-weight: 700; font-size: 2.5rem;"><i class="fas fa-user-shield"></i></h2>
                  <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">SYSTEM ADMINISTRATION</p>
                </div>
                <div>
                  <i class="fas fa-cogs" style="font-size: 2.5rem; opacity: 0.3;"></i>
                </div>
              </div>
            </div>
          </div>
          <?php endif; ?>

          <?php if ($auth->hasPermission('accept_booking')): ?>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card text-white" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%); border: none; border-radius: 10px; height: 120px; cursor: pointer;" onclick="window.location.href='accepted-bookings.php'">
              <div class="card-body d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-0" style="font-weight: 700; font-size: 2.5rem;"><?php echo $acceptedbookings; ?></h2>
                  <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">CONFIRMED</p>
                </div>
                <div>
                  <i class="fas fa-user-friends" style="font-size: 2.5rem; opacity: 0.3;"></i>
                </div>
              </div>
            </div>
          </div>
          <?php endif; ?>

          <?php if ($auth->hasPermission('view_bookings')): ?>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card text-white" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%); border: none; border-radius: 10px; height: 120px; cursor: pointer;" onclick="window.location.href='pending-bookings.php'">
              <div class="card-body d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-0" style="font-weight: 700; font-size: 2.5rem;"><?php echo $newbookings; ?></h2>
                  <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">NEW BOOKINGS</p>
                </div>
                <div>
                  <i class="fas fa-calendar-day" style="font-size: 2.5rem; opacity: 0.3;"></i>
                </div>
              </div>
            </div>
          </div>
          <?php endif; ?>

          <?php if ($auth->hasPermission('view_bookings')): ?>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card text-white" style="background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%); border: none; border-radius: 10px; height: 120px; cursor: pointer;" onclick="window.location.href='all-booking.php?filter=today'">
              <div class="card-body d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-0" style="font-weight: 700; font-size: 2.5rem;"><?php echo $todaysBookings; ?></h2>
                  <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">TODAY</p>
                </div>
                <div>
                  <i class="fas fa-calendar-check" style="font-size: 2.5rem; opacity: 0.3;"></i>
                </div>
              </div>
            </div>
          </div>
          <?php endif; ?>

          <?php if ($auth->hasPermission('manage_boats')): ?>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card text-white" style="background: linear-gradient(135deg, #3F51B5 0%, #303F9F 100%); border: none; border-radius: 10px; height: 120px; cursor: pointer;" onclick="window.location.href='manage-boat.php'" title="Manage available boats">
              <div class="card-body d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-0" style="font-weight: 700; font-size: 2.5rem;"><?php echo $availableBoats; ?></h2>
                  <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">AVAILABLE BOATS</p>
                </div>
                <div>
                  <i class="fas fa-anchor" style="font-size: 2.5rem; opacity: 0.3;" title="Available Boats"></i>
                </div>
              </div>
            </div>
          </div>
          <?php endif; ?>

          <?php if ($auth->hasPermission('view_bookings')): ?>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card text-white" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%); border: none; border-radius: 10px; height: 120px; cursor: pointer;" onclick="window.location.href='all-booking.php'">
              <div class="card-body d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-0" style="font-weight: 700; font-size: 2.5rem;"><?php echo round($totalPassengers / max($totalReservations, 1), 1); ?></h2>
                  <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">AVG PAX/BOOKING</p>
                </div>
                <div>
                  <i class="fas fa-check-circle" style="font-size: 2.5rem; opacity: 0.3;"></i>
                </div>
              </div>
            </div>
          </div>
          <?php endif; ?>

          <?php if ($auth->hasPermission('reject_booking')): ?>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card text-white" style="background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%); border: none; border-radius: 10px; height: 120px; cursor: pointer;" onclick="window.location.href='rejected-bookings.php'">
              <div class="card-body d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-0" style="font-weight: 700; font-size: 2.5rem;"><?php echo $rejectedbookings; ?></h2>
                  <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">CANCELLED</p>
                </div>
                <div>
                  <i class="fas fa-times-circle" style="font-size: 2.5rem; opacity: 0.3;"></i>
                </div>
              </div>
            </div>
          </div>
          <?php endif; ?>
        </div>

        <!-- QUICK ACTIONS -->
        <h4 class="dashboard-section-title">
          <i class="fas fa-bolt"></i> Quick Actions
        </h4>
        <div class="row justify-content-center mb-4">
          <?php if ($auth->hasPermission('view_bookings')): ?>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="all-booking.php?filter=today" class="btn btn-info btn-block btn-lg">
              <i class="fas fa-calendar-day"></i><br>
              <small>Today's Bookings</small>
            </a>
          </div>
          <?php endif; ?>
          
          <?php if ($auth->hasPermission('view_bookings')): ?>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="pending-bookings.php" class="btn btn-warning btn-block btn-lg">
              <i class="fas fa-plus-circle"></i><br>
              <small>New Bookings</small>
            </a>
          </div>
          <?php endif; ?>
          
          <?php if ($auth->hasPermission('view_passenger_manifest')): ?>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="passenger-manifest.php" class="btn btn-primary btn-block btn-lg">
              <i class="fas fa-users"></i><br>
              <small>Passenger Manifest</small>
            </a>
          </div>
          <?php endif; ?>
          
          <?php if ($auth->hasPermission('view_bookings')): ?>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="all-booking.php" class="btn btn-secondary btn-block btn-lg">
              <i class="fas fa-list"></i><br>
              <small>All Bookings</small>
            </a>
          </div>
          <?php endif; ?>

          <?php if ($auth->hasPermission('manage_boats')): ?>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="manage-boat.php" class="btn btn-info btn-block btn-lg">
              <i class="fas fa-ship"></i><br>
              <small>Manage Boats</small>
            </a>
          </div>
          <?php endif; ?>

          <?php if ($auth->hasPermission('view_reports')): ?>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="bw-dates-report.php" class="btn btn-primary btn-block btn-lg">
              <i class="fas fa-chart-bar"></i><br>
              <small>Reports</small>
            </a>
          </div>
          <?php endif; ?>

          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="../website/pages/online-booking.php" target="_blank" class="btn btn-dark btn-block btn-lg">
              <i class="fas fa-external-link-alt"></i><br>
              <small>Booking Site</small>
            </a>
          </div>
        </div>

        <!-- RECENT BOOKINGS SECTION -->
        <?php if ($auth->hasPermission('view_bookings')): ?>
        <div class="row">
          <div class="col-12">
            <h4 class="dashboard-section-title">
              <i class="fas fa-clock"></i> Recent Bookings
            </h4>
            <div class="card">
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>Booking ID</th>
                        <th>Customer</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Passengers</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <?php
                      try {
                        // Fix the query to use correct column names from bookings table
                        $stmt = $con->prepare("SELECT booking_id, CONCAT(first_name, ' ', last_name) as customer_name, start_date as booking_date, booking_status, no_of_pax as total_passengers, created_at FROM bookings ORDER BY created_at DESC LIMIT 5");
                        $stmt->execute();
                        $result = $stmt->get_result();

                        if ($result->num_rows > 0) {
                          while ($row = $result->fetch_assoc()) {
                            $status_class = '';
                            switch ($row['booking_status']) {
                              case 'confirmed':
                              case 'accepted': $status_class = 'badge-success'; break;
                              case 'pending': $status_class = 'badge-warning'; break;
                              case 'cancelled':
                              case 'rejected': $status_class = 'badge-danger'; break;
                              default: $status_class = 'badge-secondary';
                            }

                            echo "<tr>";
                            echo "<td><strong>#{$row['booking_id']}</strong></td>";
                            echo "<td>{$row['customer_name']}</td>";
                            echo "<td>" . date('M j, Y', strtotime($row['booking_date'])) . "</td>";
                            echo "<td><span class='badge {$status_class}'>" . ucfirst($row['booking_status']) . "</span></td>";
                            echo "<td>{$row['total_passengers']}</td>";
                            echo "<td>";
                            echo "<a href='view-booking.php?id={$row['booking_id']}' class='btn btn-sm btn-info'><i class='fas fa-eye'></i></a> ";
                            if ($auth->hasPermission('edit_booking')) {
                              echo "<a href='edit-booking.php?id={$row['booking_id']}' class='btn btn-sm btn-warning'><i class='fas fa-edit'></i></a>";
                            }
                            echo "</td>";
                            echo "</tr>";
                          }
                        } else {
                          echo "<tr><td colspan='6' class='text-center'>No recent bookings found.</td></tr>";
                        }
                      } catch (Exception $e) {
                        echo "<tr><td colspan='6' class='text-center text-danger'>Error loading bookings: " . $e->getMessage() . "</td></tr>";
                      }
                      ?>
                    </tbody>
                  </table>
                </div>
                <div class="text-center mt-3">
                  <a href="all-booking.php" class="btn btn-primary">
                    <i class="fas fa-list mr-2"></i>View All Bookings
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <?php endif; ?>

        <!-- BASIC CHARTS SECTION (Available to all dashboard users) -->
        <div class="row mt-4">
          <!-- Gender Distribution -->
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-chart-donut mr-2"></i>Passenger Gender Distribution
                </h3>
              </div>
              <div class="card-body">
                <canvas id="genderChart" style="height: 300px;"></canvas>
              </div>
            </div>
          </div>

          <!-- Booking Status Distribution -->
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-chart-pie mr-2"></i>Booking Status Distribution
                </h3>
              </div>
              <div class="card-body">
                <canvas id="statusChart" style="height: 300px;"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- ADVANCED CHARTS SECTION (Requires view_reports permission) -->
        <?php if ($auth->hasPermission('view_reports')): ?>
        <div class="row mt-4">
          <!-- Monthly Bookings Chart -->
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-chart-line mr-2"></i>Monthly Bookings Trend
                </h3>
              </div>
              <div class="card-body">
                <canvas id="monthlyBookingsChart" style="height: 300px;"></canvas>
              </div>
            </div>
          </div>

          <!-- Age Group Distribution -->
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-chart-bar mr-2"></i>Age Group Distribution
                </h3>
              </div>
              <div class="card-body">
                <canvas id="ageChart" style="height: 300px;"></canvas>
              </div>
            </div>
          </div>
        </div>
        <?php endif; ?>

      </div>
    </section>
  </div>

  <!-- Footer -->
  <?php include('../includes/footer.php'); ?>
</div>

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>

<script>
$(document).ready(function() {
    console.log('Clean dashboard loaded successfully!');

    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);

    // Initialize basic charts (available to all dashboard users)
    initializeBasicCharts();

    <?php if ($auth->hasPermission('view_reports')): ?>
    // Initialize advanced charts (requires view_reports permission)
    initializeAdvancedCharts();
    <?php endif; ?>
});

// Initialize basic charts available to all dashboard users
function initializeBasicCharts() {
    // Status Distribution Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Confirmed', 'Pending', 'Cancelled'],
            datasets: [{
                data: [<?php echo $acceptedbookings; ?>, <?php echo $newbookings; ?>, <?php echo $rejectedbookings; ?>],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(255, 99, 132, 0.8)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(255, 99, 132, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Gender Distribution Chart - Get real data from passenger_manifest
    $.ajax({
        url: 'get-gender-stats.php', // Correct path since dashboard.php and get-gender-stats.php are in the same folder
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            console.log('Gender stats response:', response);
            const genderCtx = document.getElementById('genderChart').getContext('2d');
            new Chart(genderCtx, {
                type: 'pie',
                data: {
                    labels: ['Male', 'Female'],
                    datasets: [{
                        data: [response.male || 0, response.female || 0],
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 99, 132, 0.8)'
                        ],
                        borderColor: [
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 99, 132, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        },
        error: function(xhr, status, error) {
            console.log('Gender stats AJAX error:', status, error, xhr.responseText);
            // Fallback with sample data
            const genderCtx = document.getElementById('genderChart').getContext('2d');
            new Chart(genderCtx, {
                type: 'pie',
                data: {
                    labels: ['Male', 'Female'],
                    datasets: [{
                        data: [45, 55],
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 99, 132, 0.8)'
                        ],
                        borderColor: [
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 99, 132, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    });
}

<?php if ($auth->hasPermission('view_reports')): ?>
// Initialize advanced charts (requires view_reports permission)
function initializeAdvancedCharts() {
    // Monthly Bookings Chart
    const monthlyCtx = document.getElementById('monthlyBookingsChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Bookings',
                data: [<?php echo $newbookings; ?>, <?php echo $acceptedbookings; ?>, <?php echo $rejectedbookings; ?>, <?php echo $todaysBookings; ?>, <?php echo $totalReservations; ?>, <?php echo $totalPassengers; ?>],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Age Group Distribution Chart - Get real data
    $.ajax({
        url: 'get-age-stats.php',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            const ageCtx = document.getElementById('ageChart').getContext('2d');
            new Chart(ageCtx, {
                type: 'bar',
                data: {
                    labels: ['0-17', '18-30', '31-45', '46-60', '60+'],
                    datasets: [{
                        label: 'Passengers',
                        data: [
                            response.age_0_17 || 0,
                            response.age_18_30 || 0,
                            response.age_31_45 || 0,
                            response.age_46_60 || 0,
                            response.age_60_plus || 0
                        ],
                        backgroundColor: 'rgba(153, 102, 255, 0.8)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },
        error: function() {
            // Fallback with sample data
            const ageCtx = document.getElementById('ageChart').getContext('2d');
            new Chart(ageCtx, {
                type: 'bar',
                data: {
                    labels: ['0-17', '18-30', '31-45', '46-60', '60+'],
                    datasets: [{
                        label: 'Passengers',
                        data: [10, 25, 30, 20, 15],
                        backgroundColor: 'rgba(153, 102, 255, 0.8)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    });
}
<?php endif; ?>
</script>

</body>
</html>
