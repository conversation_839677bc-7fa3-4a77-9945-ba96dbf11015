<?php
/**
 * Get Gender Statistics for Dashboard Charts
 */

// Define admin system constants
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}
if (!defined('ADMIN_ROOT')) {
    define('ADMIN_ROOT', __DIR__ . '/../');
}

// Include admin configuration
require_once ADMIN_ROOT . 'includes/config.php';
checkLogin(); // Use the same authentication as dashboard

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Initialize response
    $response = [
        'success' => true,
        'male' => 0,
        'female' => 0,
        'total' => 0
    ];
    
    // Check if passenger_manifest table exists
    $table_check = $con->query("SHOW TABLES LIKE 'passenger_manifest'");

    if ($table_check->num_rows > 0) {
        // Get gender distribution from passenger_manifest
        $stmt = $con->prepare("SELECT sex, COUNT(*) as count FROM passenger_manifest WHERE sex IN ('Male', 'Female') GROUP BY sex");
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            if ($row['sex'] === 'Male') {
                $response['male'] = intval($row['count']);
            } elseif ($row['sex'] === 'Female') {
                $response['female'] = intval($row['count']);
            }
        }

        $response['total'] = $response['male'] + $response['female'];
        
    } else {
        // Fallback: Get gender from bookings table
        $stmt = $con->prepare("SELECT sex, COUNT(*) as count FROM bookings WHERE sex IN ('Male', 'Female', 'male', 'female') GROUP BY sex");
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $sex = strtolower($row['sex']);
            if ($sex === 'male') {
                $response['male'] = intval($row['count']);
            } elseif ($sex === 'female') {
                $response['female'] = intval($row['count']);
            }
        }

        $response['total'] = $response['male'] + $response['female'];
    }
    
    // If no data found, provide sample data
    if ($response['total'] === 0) {
        $response['male'] = 45;
        $response['female'] = 55;
        $response['total'] = 100;
        $response['note'] = 'Sample data - no real data available';
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage(),
        'male' => 45,
        'female' => 55,
        'total' => 100
    ];
}

echo json_encode($response);
?>
